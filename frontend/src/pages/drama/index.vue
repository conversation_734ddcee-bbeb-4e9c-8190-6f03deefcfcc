<template>
  <view class="page-drama">
    <!-- Main Content -->
    <view class="main-content">
      <!-- 短剧信息 -->
      <view class="drama-info">
        <view class="drama-profile">
          <view class="drama-cover">
            <image v-if="dramaInfo.cover" :src="dramaInfo.cover" class="cover-image" mode="aspectFill" />
            <text v-else class="cover-text">封面</text>
          </view>
          <view class="drama-details">
            <text class="drama-title">{{ dramaInfo.title }}</text>
            <view class="drama-cast-section">
              <text class="cast-label">主演:</text>
              <view class="cast-list">
                <view v-for="actor in dramaInfo.actors" :key="actor.id" class="cast-item" @tap="onActorClick(actor)">
                  <view class="actor-avatar">
                    <image v-if="actor.avatar" :src="actor.avatar" class="avatar-image" mode="aspectFill" />
                    <text v-else class="avatar-text">头像</text>
                  </view>
                  <text class="actor-name">{{ actor.name }}</text>
                </view>
              </view>
            </view>
            <text class="drama-producer">出品: {{ dramaInfo.producer }}</text>
            <text class="drama-episodes">集数: {{ dramaInfo.episodes }}集</text>
            <text class="drama-read-count">播放量: {{ formatReadCount(dramaInfo.readCount) }}</text>
            <view class="drama-tags">
              <text v-for="tag in dramaInfo.tags" :key="tag" class="drama-tag" @tap="onTagClick(tag)">
                {{ tag }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 短剧简介 -->
      <view class="description-section">
        <SectionHeader title="剧情简介" :show-more="false" />
        <text class="description-text">{{ dramaInfo.description }}</text>
      </view>

      <!-- 推荐理由 -->
      <view v-if="dramaInfo.recommendation" class="recommendation-section">
        <SectionHeader title="推荐理由" :show-more="false" />
        <text class="recommendation-text">{{ dramaInfo.recommendation }}</text>
      </view>

      <!-- 精彩评论 -->
      <view class="comment-section">
        <SectionHeader title="精彩评论" :show-more="false" />

        <!-- 评论输入 -->
        <view class="comment-input" @tap="onCommentInputClick">
          <view class="comment-field-placeholder">
            <text class="placeholder-text">发一条友善的评论吧</text>
          </view>
          <view class="comment-submit">
            <text class="submit-text">发布</text>
          </view>
        </view>

        <!-- 评论列表 -->
        <view class="comment-list">
          <view v-for="comment in displayedComments" :key="comment.id" class="comment-item">
            <view class="comment-avatar">
              <text class="avatar-text">头像</text>
            </view>
            <view class="comment-content">
              <text class="comment-author">{{ comment.author }}</text>
              <text class="comment-text">{{ comment.content }}</text>
              <text class="comment-time">{{ comment.time }}</text>
            </view>
          </view>
        </view>

        <view v-if="hasMoreComments" class="comment-more" @tap="onLoadMoreComments">
          <text class="more-text">查看更多评论 ({{ remainingCommentsCount }})</text>
          <text class="material-icons more-icon">expand_more</text>
        </view>
      </view>

      <!-- 相关报道 - 暂时隐藏 -->
      <!-- <view class="news-section">
        <SectionHeader title="相关报道" :show-more="false" />
        <view class="news-list">
          <view v-for="news in newsList" :key="news.id" class="news-item" @tap="onNewsClick(news)">
            <view class="news-content">
              <text class="news-title">{{ news.title }}</text>
              <text class="news-source">{{ news.source }} · {{ news.time }}</text>
            </view>
            <view class="news-image">
              <text class="image-text">图片</text>
            </view>
          </view>
        </view>
      </view> -->

      <!-- 精彩Cut -->
      <view class="cut-section">
        <SectionHeader title="精彩Cut" :show-more="false" />
        <view class="cut-grid-container">
          <view class="cut-grid">
            <view v-for="cut in cutList" :key="cut.id" class="cut-item" @tap="onCutClick(cut)">
              <view class="cut-cover">
                <text class="cover-text">封面</text>
                <view class="cut-overlay">
                  <text class="material-icons play-icon">play_circle_filled</text>
                </view>
              </view>
              <text class="cut-title">{{ cut.title }}</text>
            </view>
          </view>

          <!-- 移除付费解锁遮罩 -->
        </view>
      </view>
    </view>

    <!-- 固定底部分享按钮 -->
    <view class="fixed-share-section">
      <view class="share-button" @tap="onShareClick">
        <text class="material-icons share-icon">share</text>
        <text class="share-text">分享</text>
      </view>
    </view>

    <!-- 评论弹窗 -->
    <view v-if="showCommentModal" class="comment-modal-overlay" @tap="onCloseCommentModal">
      <view class="comment-modal" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">发表评论</text>
          <view class="modal-close" @tap="onCloseCommentModal">
            <text class="material-icons">close</text>
          </view>
        </view>
        <view class="modal-content">
          <textarea class="comment-textarea" v-model="commentText" placeholder="发一条友善的评论吧..." maxlength="200"
            auto-focus />
          <view class="comment-count">{{ commentText.length }}/200</view>
        </view>
        <view class="modal-footer">
          <view class="modal-cancel" @tap="onCloseCommentModal">
            <text>取消</text>
          </view>
          <view class="modal-submit" @tap="onSubmitComment">
            <text>发布</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import './index.scss'
import { ref, computed, onMounted } from 'vue'
import SectionHeader from '../../components/SectionHeader.vue'
import Taro from '@tarojs/taro'
import { api } from '../../utils/api'

const commentText = ref('')
const totalComments = ref(2345)
const showCommentModal = ref(false)
const displayedCommentsCount = ref(3) // 初始显示3条评论
const loading = ref(false)

// 从路由参数获取短剧ID
const dramaId = ref('')
const dramaTitle = ref('')

// 加载短剧数据
const loadDramaData = async () => {
  try {
    loading.value = true
    const response = await api.getDramaById(dramaId.value)
    if (response.code === 200) {
      const data = response.data
      console.log('API响应数据:', data) // 调试日志

      // 映射API响应到前端数据结构
      dramaInfo.value = {
        title: data.bookName || '未知短剧',
        cover: null, // API暂未返回封面字段
        cast: data.actors ? data.actors.map(actor => actor.name).join(', ') : '',
        actors: data.actors ? data.actors.map(actor => ({
          id: actor.id,
          name: actor.name || '',
          avatar: actor.avatarUrl || '',
          gender: actor.gender || '',
          actInCounts: actor.actInCounts || 0,
          platform: actor.platform || ''
        })) : [],
        producer: data.authors && data.authors.length > 0 ? data.authors[0].name : '未知制作方',
        episodes: Math.floor(data.serialCount || 0), // 确保是整数
        readCount: data.readCount || 0, // 播放量/阅读量
        platform: data.platform || '', // 平台信息
        heatCount: data.heatCount || 0, // 热度
        tags: data.categories ? data.categories.map(cat => `#${cat.name}`).filter(tag => tag !== '#') : [],
        description: data.abstract || '暂无简介',
        recommendation: data.recommendation || ''
      }

      console.log('映射后的数据:', dramaInfo.value) // 调试日志
    }
  } catch (error) {
    console.error('加载短剧数据失败:', error)
    Taro.showToast({
      title: '加载数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载精彩片段
const loadDramaCuts = async () => {
  try {
    const response = await api.getDramaCuts(dramaId.value)
    if (response.code === 200) {
      cutList.value = response.data.cuts || []
    }
  } catch (error) {
    console.error('加载精彩片段失败:', error)
  }
}

// 加载评论
const loadComments = async () => {
  try {
    const response = await api.getComments({
      contentType: 'drama',
      contentId: dramaId.value
    })
    if (response.code === 200) {
      commentList.value = response.data.comments || []
      totalComments.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载评论失败:', error)
  }
}

// 获取路由参数
onMounted(() => {
  const instance = Taro.getCurrentInstance()
  if (instance.router && instance.router.params) {
    dramaId.value = instance.router.params.id || ''
    dramaTitle.value = decodeURIComponent(instance.router.params.title || '')

    if (dramaId.value) {
      loadDramaData()
      loadDramaCuts()
      loadComments()
    }
  }
})

// 短剧信息
const dramaInfo = ref({
  title: '霸道总裁爱上我',
  cover: null,
  cast: '张三, 李四, 王五',
  actors: [
    { id: 1, name: '张三', avatar: '', gender: '', actInCounts: 0, platform: '' },
    { id: 2, name: '李四', avatar: '', gender: '', actInCounts: 0, platform: '' },
    { id: 3, name: '王五', avatar: '', gender: '', actInCounts: 0, platform: '' }
  ],
  producer: '我爱看短剧集团',
  episodes: 100,
  readCount: 0,
  platform: '',
  heatCount: 0,
  tags: ['#霸道总裁', '#都市爱情', '#超甜'],
  description: '这里是简介',
  recommendation: '这部短剧以其紧凑的剧情、高颜值的演员阵容和精良的制作，成功吸引了大量观众。讲述了一个普通女孩与霸道总裁之间，从最初的误会重重到最终甜蜜牵手的浪漫爱情故事。剧情反转不断，甜虐交织，让人欲罢不能！'
})

// 评论列表（扩展更多评论数据）
const commentList = ref([
  {
    id: 1,
    author: '小甜甜',
    content: '太上头了！男主也太帅了吧！我的少女心泛滥了！',
    time: '2小时前'
  },
  {
    id: 2,
    author: '剧迷一个',
    content: '剧情有点老套，但是架不住演员演得好啊，很下饭。',
    time: '5小时前'
  },
  {
    id: 3,
    author: '用户C',
    content: '期待后续剧情发展，已经追到最新了',
    time: '6小时前'
  },
  {
    id: 4,
    author: '用户D',
    content: '这个短剧真的很不错，推荐大家看',
    time: '8小时前'
  },
  {
    id: 5,
    author: '用户E',
    content: '男主颜值太高了，女主也很美',
    time: '10小时前'
  },
  {
    id: 6,
    author: '用户F',
    content: '剧情反转太精彩了，完全猜不到',
    time: '12小时前'
  }
])

// 计算属性：当前显示的评论
const displayedComments = computed(() => {
  return commentList.value.slice(0, displayedCommentsCount.value)
})

// 计算属性：是否还有更多评论
const hasMoreComments = computed(() => {
  return displayedCommentsCount.value < commentList.value.length
})

// 计算属性：剩余评论数量
const remainingCommentsCount = computed(() => {
  return commentList.value.length - displayedCommentsCount.value
})

// 新闻列表
const newsList = ref([
  {
    id: 1,
    title: '《霸道总裁爱上我》为何能火爆全网？背后原因竟是...',
    source: '凤凰网娱乐',
    time: '3小时前'
  },
  {
    id: 2,
    title: '专访男主角张三：现实中的我其实很害羞',
    source: '新浪看点',
    time: '1天前'
  }
])

// Cut列表
const cutList = ref([
  { id: 1, title: '霸道总裁初遇女主' },
  { id: 2, title: '甜蜜告白片段' },
  { id: 3, title: '经典吻戏合集' },
  { id: 4, title: '搞笑花絮大放送' },
  { id: 5, title: '幕后花絮' },
  { id: 6, title: '精彩片段合集' }
])

// 工具函数
// 格式化播放量显示
function formatReadCount(count) {
  if (!count || count === 0) return '0'

  if (count >= 100000000) {
    return `${(count / 100000000).toFixed(1)}亿`
  } else if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`
  } else {
    return count.toString()
  }
}

// 事件处理函数
// 演员点击事件
function onActorClick(actor) {
  console.log('跳转到演员页面:', actor.name)
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/actor/index?id=${actor.id}&name=${encodeURIComponent(actor.name || '')}`
    })
  }
}

// 标签点击事件
function onTagClick(tag) {
  // 移除标签前的#号
  const tagName = tag.replace('#', '')
  console.log('跳转到标签页面:', tagName)
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/tag/index?name=${encodeURIComponent(tagName)}`
    })
  }
}

// 加载更多评论
function onLoadMoreComments() {
  console.log('加载更多评论')
  // 每次增加3条评论
  displayedCommentsCount.value = Math.min(
    displayedCommentsCount.value + 3,
    commentList.value.length
  )
}

// 评论相关功能
function onCommentInputClick() {
  showCommentModal.value = true
}

function onCloseCommentModal() {
  showCommentModal.value = false
}

function onSubmitComment() {
  if (commentText.value.trim()) {
    console.log('发布评论:', commentText.value)
    // 这里可以添加实际的评论提交逻辑

    // 添加到评论列表
    const newComment = {
      id: Date.now(),
      author: '我',
      content: commentText.value,
      time: '刚刚'
    }
    commentList.value.unshift(newComment)
    totalComments.value++

    // 清空输入框并关闭弹窗
    commentText.value = ''
    showCommentModal.value = false

    // 显示成功提示
    if (typeof Taro !== 'undefined' && Taro.showToast) {
      Taro.showToast({
        title: '评论发布成功',
        icon: 'success'
      })
    }
  }
}



function onNewsClick(news) {
  console.log('点击新闻:', news)
  // 可以跳转到新闻详情页面或外部链接
}

// 分享功能
function onShareClick() {
  console.log('分享短剧')
  if (typeof Taro !== 'undefined' && Taro.showActionSheet) {
    Taro.showActionSheet({
      itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
      success: (res) => {
        const actions = ['微信', '朋友圈', '复制链接']
        console.log(`分享到${actions[res.tapIndex]}`)

        if (res.tapIndex === 2) {
          // 复制链接
          if (Taro.setClipboardData) {
            Taro.setClipboardData({
              data: `我发现了一部超好看的短剧《${dramaInfo.value.title}》，快来一起看吧！`,
              success: () => {
                Taro.showToast({
                  title: '链接已复制',
                  icon: 'success'
                })
              }
            })
          }
        } else {
          // 其他分享方式
          Taro.showToast({
            title: '分享成功',
            icon: 'success'
          })
        }
      }
    })
  }
}

function onCutClick(cut) {
  console.log('点击Cut:', cut)
  console.log('播放内容:', cut.title)
  // 可以跳转到视频播放页面
}
</script>
