-- 我爱看短剧数据库创建脚本
-- 基于 api-spec.json 中的数据模型生成

-- 使用数据库
USE kanduanju;


-- 用户表已存在，使用现有的 users 表结构：
CREATE TABLE `users` ( 
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `nickname` VA<PERSON>HA<PERSON>(255) NULL,
  `username` VA<PERSON><PERSON><PERSON>(255) NULL,
  `email` VARCHAR(255) NULL,
  `phone` VARCHAR(255) NULL,
  `password` VARCHAR(255) NULL,
  `password_change_tz` BIGINT NULL,
  `app_lang` VARCHAR(255) NULL,
  `reset_token` VARCHAR(255) NULL,
  `system_settings` JSON NULL,
  `sort` BIGINT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
   PRIMARY KEY (`id`),
  CONSTRAINT `username` UNIQUE (`username`),
  CONSTRAINT `reset_token` UNIQUE (`reset_token`),
  CONSTRAINT `phone` UNIQUE (`phone`),
  CONSTRAINT `email` UNIQUE (`email`)
)
ENGINE = InnoDB;
CREATE INDEX `users_updated_by_id` 
ON `users` (
  `updated_by_id` ASC
);
CREATE INDEX `users_created_by_id` 
ON `users` (
  `created_by_id` ASC
);


-- 短剧表 #需要使用
CREATE TABLE `duanju` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `book_name` VARCHAR(255) NULL,
  `book_id` VARCHAR(255) NULL,
  `serial_count` DOUBLE NULL,
  `read_count` BIGINT NULL,
  `read_count_last_updated_ts` BIGINT NULL,
  `abstract` TEXT NULL,
  `platform` VARCHAR(255) NULL,
  `recommendation` TEXT NULL,
  `is_public` TINYINT NULL DEFAULT 1 ,
  `heat_count` BIGINT NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_created_by_id` 
ON `duanju` (
  `created_by_id` ASC
);
CREATE INDEX `duanju_updated_by_id` 
ON `duanju` (
  `updated_by_id` ASC
);


-- 演员表 #需要使用
CREATE TABLE `duanju_actors` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `name` VARCHAR(255) NULL,
  `platform` VARCHAR(255) NULL,
  `avatar_url` VARCHAR(255) NULL,
  `xhs_id` VARCHAR(255) NULL,
  `weibo_id` VARCHAR(255) NULL,
  `webo_super_index_id` VARCHAR(255) NULL,
  `act_in_counts` BIGINT NULL,
  `desc` TEXT NULL,
  `douyin_id` VARCHAR(255) NULL,
  `gender` VARCHAR(255) NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_actors_created_by_id` 
ON `duanju_actors` (
  `created_by_id` ASC
);
CREATE INDEX `duanju_actors_updated_by_id` 
ON `duanju_actors` (
  `updated_by_id` ASC
);


-- 分类表 #需要使用
CREATE TABLE `duanju_categories` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `name` VARCHAR(255) NULL,
  `platform` VARCHAR(255) NULL,
  `cat_id` VARCHAR(255) NULL,
  `f_4vpgcuhqxjl` BIGINT NULL,
  `heat_count` BIGINT NULL,
  `desc` TEXT NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_categories_f_4vpgcuhqxjl` 
ON `duanju_categories` (
  `f_4vpgcuhqxjl` ASC
);
CREATE INDEX `duanju_categories_updated_by_id` 
ON `duanju_categories` (
  `updated_by_id` ASC
);


-- 精彩片段表 #需要使用
CREATE TABLE `duanju_actor_cuts` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `title` VARCHAR(255) NULL,
  `desc` TEXT NULL,
  `play_count` BIGINT NULL,
  `duration` BIGINT NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_actor_cuts_updated_by_id` 
ON `duanju_actor_cuts` (
  `updated_by_id` ASC
);

-- 精彩片段表 #需要使用
CREATE TABLE `duanju_series_cuts` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `title` VARCHAR(255) NULL,
  `desc` TEXT NULL,
  `duration` BIGINT NULL,
  `play_count` BIGINT NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_series_cuts_created_by_id` 
ON `duanju_series_cuts` (
  `created_by_id` ASC
);
CREATE INDEX `duanju_series_cuts_updated_by_id` 
ON `duanju_series_cuts` (
  `updated_by_id` ASC
);


-- 新闻表
CREATE TABLE `duanju_news` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `title` VARCHAR(255) NULL,
  `description` TEXT NULL,
  `content` LONGTEXT NULL,
  `source` VARCHAR(255) NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_news_updated_by_id` 
ON `duanju_news` (
  `updated_by_id` ASC
);


-- 评论表 演员
CREATE TABLE `duanju_actor_comments` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `content` TEXT NULL,
  `status` TINYINT NULL DEFAULT 1 ,
  `f_bd9p0idzl6i` BIGINT NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_actor_comments_f_bd9p0idzl6i` 
ON `duanju_actor_comments` (
  `f_bd9p0idzl6i` ASC
);
CREATE INDEX `duanju_actor_comments_updated_by_id` 
ON `duanju_actor_comments` (
  `updated_by_id` ASC
);


CREATE TABLE `duanju_comments` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `content` TEXT NULL,
  `status` TINYINT NULL DEFAULT 1 ,
  `f_l77p9koj8sc` BIGINT NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `duanju_comments_updated_by_id` 
ON `duanju_comments` (
  `updated_by_id` ASC
);
CREATE INDEX `duanju_comments_f_l77p9koj8sc` 
ON `duanju_comments` (
  `f_l77p9koj8sc` ASC
);


-- 付费订单表
CREATE TABLE payment_orders (
    id VARCHAR(50) PRIMARY KEY,
    user_id BIGINT NOT NULL,
    content_type ENUM('drama', 'cut') NOT NULL,
    content_id VARCHAR(50) NOT NULL,
    payment_method ENUM('wechat', 'alipay', 'apple_pay') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    unlocked_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_content (content_type, content_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='付费订单表';

-- 今日运势表 #需要使用
CREATE TABLE `daily_fortune` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `today` DATE NULL,
  `good_text` VARCHAR(255) NULL,
  `bad_text` VARCHAR(255) NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE INDEX `daily_fortune_updated_by_id` 
ON `daily_fortune` (
  `updated_by_id` ASC
);

-- 排行榜配置表
CREATE TABLE ranking_configs (
    id VARCHAR(50) PRIMARY KEY,
    type ENUM('hot', 'weird', 'actor_total', 'actor_male', 'actor_female') NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status TINYINT DEFAULT 1 COMMENT '1:启用 0:禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜配置表';

-- 应用配置表 (重命名避免与现有system_configs冲突)
CREATE TABLE app_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用配置表';

CREATE TABLE `collections` ( 
  `key` VARCHAR(255) NOT NULL,
  `name` VARCHAR(255) NULL,
  `title` VARCHAR(255) NULL,
  `inherit` TINYINT NULL DEFAULT 0 ,
  `hidden` TINYINT NULL DEFAULT 0 ,
  `options` JSON NULL,
  `description` VARCHAR(255) NULL,
  `sort` BIGINT NULL,
   PRIMARY KEY (`key`),
  CONSTRAINT `name` UNIQUE (`name`)
)
ENGINE = InnoDB;
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('0yt2067mgim', 'roles', '{{t("Roles")}}', 0, 0, '{"from":"db2cm","model":"RoleModel","origin":"@nocobase/plugin-acl","sortable":true,"autoGenId":false,"dumpRules":"required","underscored":true,"migrationRules":["overwrite","schema-only"],"filterTargetKey":"name"}', 'Role data', '1');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('3c6mw4bshuo', 't_ofejlo6jzf9', '短剧-分类-中间表', 0, 0, '{"sortable":false,"autoGenId":false,"isThrough":true,"autoCreate":true,"timestamps":true}', NULL, '9');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('4222n72fb70', 'sources', '数据源', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"name"}', NULL, '5');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('4tkhpfv0urc', 't_5ox8ukut7za', 't_5ox8ukut7za', 0, 0, '{"sortable":false,"autoGenId":false,"isThrough":true,"autoCreate":true,"timestamps":true}', NULL, '11');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('5pr66gq9fmq', 'daily_fortune', '今日运势', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"today","filterTargetKey":["id"],"unavailableActions":[]}', NULL, '23');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('5zxya40rrs3', 't_z8qypwvydhh', 't_z8qypwvydhh', 0, 0, '{"sortable":false,"autoGenId":false,"isThrough":true,"autoCreate":true,"timestamps":true}', NULL, '18');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('6ly97jsp5e0', 'duanju_categories', '短剧分类', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"name"}', NULL, '8');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('6oms30werbz', 'duanju_actors', '短剧演员', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"name","filterTargetKey":"id","unavailableActions":[]}', NULL, '14');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('ai78yqisaum', 't_o4wwn0qrn4t', '短剧-演员-中间表', 0, 0, '{"sortable":false,"autoGenId":false,"isThrough":true,"autoCreate":true,"timestamps":true,"filterTargetKey":["f_r9xdgapk60j"],"unavailableActions":[]}', NULL, '17');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('d7mwsobtq8z', 'duanju', '短剧', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"book_name"}', NULL, '7');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('eelawyhbcep', 'duanju_actor_cuts', '演员 Cuts', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"title"}', NULL, '20');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('er0fqbiw9gc', 'machines', '机器', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"name"}', NULL, '4');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('fgaiaiqtxgp', 'departments', '{{t("Departments")}}', 0, 0, '{"from":"db2cm","tree":"adjacency-list","model":"DepartmentModel","origin":"@nocobase/plugin-departments","shared":true,"logging":true,"sortable":true,"template":"tree","createdBy":true,"dumpRules":"required","updatedBy":true,"underscored":true,"migrationRules":["overwrite"]}', NULL, '3');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('g8a8znraqus', 'duanju_series_cuts', '短剧  剧集 Cuts', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"title"}', NULL, '21');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('h4xqjmybwgo', 't_q95zevchrgq', '用户-演员-中间表', 0, 0, '{"sortable":false,"autoGenId":false,"isThrough":true,"autoCreate":true,"timestamps":true}', NULL, '22');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('h62p6jmsgav', 'duanju_episodes', '短剧剧集', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"title","filterTargetKey":"id","unavailableActions":[]}', NULL, '15');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('jcmetdvem75', 'source_categories', '数据源分类', 0, 0, '{"tree":"adjacencyList","view":false,"logging":true,"template":"tree","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"name"}', NULL, '6');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('lcuvhnhjkdv', 'duanju_comments', '短剧评论', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true}', NULL, '26');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('le1t0120ic8', 'duanju_actor_comments', '演员评论', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true}', NULL, '25');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('o9ry8bzdydh', 'duanju_authors', '短剧制作方', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"name","filterTargetKey":"id","unavailableActions":[]}', NULL, '13');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('u0e1fcqqwks', 'duanju_news', '短剧新闻', 0, 0, '{"view":false,"logging":true,"template":"general","autoGenId":false,"createdAt":true,"createdBy":true,"updatedAt":true,"updatedBy":true,"titleField":"title"}', NULL, '24');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('vl6hm09yhak', 'users', '{{t("Users")}}', 0, 0, '{"from":"db2cm","model":"UserModel","origin":"@nocobase/plugin-users","shared":true,"logging":true,"sortable":"sort","createdBy":true,"dumpRules":{"group":"user"},"updatedBy":true,"underscored":true,"migrationRules":["schema-only","overwrite","schema-only","overwrite","skip"]}', NULL, '2');
INSERT INTO `collections` (`key`, `name`, `title`, `inherit`, `hidden`, `options`, `description`, `sort`) VALUES ('ycu3lmvfwcw', 't_mjtaw8zxvjl', '短剧-制作方-中间表', 0, 0, '{"sortable":false,"autoGenId":false,"isThrough":true,"autoCreate":true,"timestamps":true,"filterTargetKey":["f_8nltnm6gzxj"],"unavailableActions":[]}', NULL, '16');


CREATE TABLE `attachments` ( 
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `title` VARCHAR(255) NULL COMMENT '用户文件名（不含扩展名）' ,
  `filename` VARCHAR(255) NULL COMMENT '系统文件名（含扩展名）' ,
  `extname` VARCHAR(255) NULL COMMENT '扩展名（含“.”）' ,
  `size` INT NULL COMMENT '文件体积（字节）' ,
  `mimetype` VARCHAR(255) NULL,
  `path` TEXT NULL COMMENT '相对路径（含“/”前缀）' ,
  `meta` JSON NULL COMMENT '其他文件信息（如图片的宽高）' ,
  `url` TEXT NULL COMMENT '网络访问地址' ,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `storage_id` BIGINT NULL,
   PRIMARY KEY (`id`)
)
ENGINE = InnoDB;
CREATE TABLE `t_0fthjzbjs5d` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_70ahxneq20b` BIGINT NOT NULL,
  `f_4dmwj745nl5` BIGINT NOT NULL,
   PRIMARY KEY (`f_70ahxneq20b`, `f_4dmwj745nl5`)
)
ENGINE = InnoDB;
CREATE TABLE `t_1swvkicqqyi` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_c4reuq3d75b` BIGINT NOT NULL,
  `f_pk0a2xpl2rq` BIGINT NOT NULL,
   PRIMARY KEY (`f_c4reuq3d75b`, `f_pk0a2xpl2rq`)
)
ENGINE = InnoDB;
CREATE TABLE `t_5ox8ukut7za` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_eynccd6085v` BIGINT NOT NULL,
  `f_7x7q55bxj0s` BIGINT NOT NULL,
   PRIMARY KEY (`f_eynccd6085v`, `f_7x7q55bxj0s`)
)
ENGINE = InnoDB;
CREATE TABLE `t_96gqmckoo49` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_fuaxh9apo74` BIGINT NOT NULL,
  `f_87wjpnvhsnk` BIGINT NOT NULL,
   PRIMARY KEY (`f_fuaxh9apo74`, `f_87wjpnvhsnk`)
)
ENGINE = InnoDB;
CREATE TABLE `t_cznrv1qts8w` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_xwkmzj5zqm4` BIGINT NOT NULL,
  `f_0ean1gy9mz6` BIGINT NOT NULL,
   PRIMARY KEY (`f_xwkmzj5zqm4`, `f_0ean1gy9mz6`)
)
ENGINE = InnoDB;
CREATE TABLE `t_dtanqoswyvk` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_lt9m395nq2e` BIGINT NOT NULL,
  `f_ksjv4v1pb6i` BIGINT NOT NULL,
   PRIMARY KEY (`f_lt9m395nq2e`, `f_ksjv4v1pb6i`)
)
ENGINE = InnoDB;
CREATE TABLE `t_jbg3wc4gf2p` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_c6r5x59al8t` BIGINT NOT NULL,
  `f_xyjz8of2otj` BIGINT NOT NULL,
   PRIMARY KEY (`f_c6r5x59al8t`, `f_xyjz8of2otj`)
)
ENGINE = InnoDB;
CREATE TABLE `t_l1vh9zq1yev` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_jd4eirdfs65` BIGINT NOT NULL,
  `f_jc3hm6yuqaq` BIGINT NOT NULL,
   PRIMARY KEY (`f_jd4eirdfs65`, `f_jc3hm6yuqaq`)
)
ENGINE = InnoDB;
CREATE TABLE `t_mjtaw8zxvjl` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_8nltnm6gzxj` BIGINT NOT NULL,
  `f_hlfdisq43sx` BIGINT NOT NULL,
   PRIMARY KEY (`f_8nltnm6gzxj`, `f_hlfdisq43sx`)
)
ENGINE = InnoDB;
CREATE TABLE `t_o4wwn0qrn4t` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_r9xdgapk60j` BIGINT NOT NULL,
  `f_q8e962zcn20` BIGINT NOT NULL,
   PRIMARY KEY (`f_r9xdgapk60j`, `f_q8e962zcn20`)
)
ENGINE = InnoDB;
CREATE TABLE `t_ofejlo6jzf9` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_e6dqsc9q8j7` BIGINT NOT NULL,
  `f_xeblmd9tsf8` BIGINT NOT NULL,
   PRIMARY KEY (`f_e6dqsc9q8j7`, `f_xeblmd9tsf8`)
)
ENGINE = InnoDB;
CREATE TABLE `t_q95zevchrgq` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_wjxm1ueyqdw` BIGINT NOT NULL,
  `f_6edhr0wcmio` BIGINT NOT NULL,
   PRIMARY KEY (`f_wjxm1ueyqdw`, `f_6edhr0wcmio`)
)
ENGINE = InnoDB;
CREATE TABLE `t_z8qypwvydhh` ( 
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NOT NULL,
  `f_wxdjvw40f8v` BIGINT NOT NULL,
  `f_94r7d6ql3qx` BIGINT NOT NULL,
   PRIMARY KEY (`f_wxdjvw40f8v`, `f_94r7d6ql3qx`)
)
ENGINE = InnoDB;
CREATE INDEX `attachments_updated_by_id` 
ON `attachments` (
  `updated_by_id` ASC
);
CREATE INDEX `t_0fthjzbjs5d_f_4dmwj745nl5` 
ON `t_0fthjzbjs5d` (
  `f_4dmwj745nl5` ASC
);
CREATE INDEX `t_1swvkicqqyi_f_pk0a2xpl2rq` 
ON `t_1swvkicqqyi` (
  `f_pk0a2xpl2rq` ASC
);
CREATE INDEX `t_5ox8ukut7za_f_7x7q55bxj0s` 
ON `t_5ox8ukut7za` (
  `f_7x7q55bxj0s` ASC
);
CREATE INDEX `t_96gqmckoo49_f_87wjpnvhsnk` 
ON `t_96gqmckoo49` (
  `f_87wjpnvhsnk` ASC
);
CREATE INDEX `t_cznrv1qts8w_f_0ean1gy9mz6` 
ON `t_cznrv1qts8w` (
  `f_0ean1gy9mz6` ASC
);
CREATE INDEX `t_dtanqoswyvk_f_ksjv4v1pb6i` 
ON `t_dtanqoswyvk` (
  `f_ksjv4v1pb6i` ASC
);
CREATE INDEX `t_jbg3wc4gf2p_f_xyjz8of2otj` 
ON `t_jbg3wc4gf2p` (
  `f_xyjz8of2otj` ASC
);
CREATE INDEX `t_l1vh9zq1yev_f_jc3hm6yuqaq` 
ON `t_l1vh9zq1yev` (
  `f_jc3hm6yuqaq` ASC
);
CREATE INDEX `t_mjtaw8zxvjl_f_hlfdisq43sx` 
ON `t_mjtaw8zxvjl` (
  `f_hlfdisq43sx` ASC
);
CREATE INDEX `t_mjtaw8zxvjl_f_8nltnm6gzxj` 
ON `t_mjtaw8zxvjl` (
  `f_8nltnm6gzxj` ASC
);
CREATE INDEX `t_o4wwn0qrn4t_f_q8e962zcn20` 
ON `t_o4wwn0qrn4t` (
  `f_q8e962zcn20` ASC
);
CREATE INDEX `t_o4wwn0qrn4t_f_r9xdgapk60j` 
ON `t_o4wwn0qrn4t` (
  `f_r9xdgapk60j` ASC
);
CREATE INDEX `t_ofejlo6jzf9_f_xeblmd9tsf8` 
ON `t_ofejlo6jzf9` (
  `f_xeblmd9tsf8` ASC
);
CREATE INDEX `t_ofejlo6jzf9_f_e6dqsc9q8j7` 
ON `t_ofejlo6jzf9` (
  `f_e6dqsc9q8j7` ASC
);
CREATE INDEX `t_q95zevchrgq_f_6edhr0wcmio` 
ON `t_q95zevchrgq` (
  `f_6edhr0wcmio` ASC
);
CREATE INDEX `t_z8qypwvydhh_f_94r7d6ql3qx` 
ON `t_z8qypwvydhh` (
  `f_94r7d6ql3qx` ASC
);

